use crate::config::AppConfig;
use crate::editor::types::ProposedEdit;
use crate::block_parsing::traits::ParsableBlock;
use crate::expert::runner::ExpertFlowOutcome;
use crate::files::ordered_files::OrderedFiles; // Added for OrderedFiles
use crate::llm::{ChatMessage, ChatRole, LLMClient, MessageType}; // Added ChatRole, MessageType
use log::{debug, error, info, trace, warn};
use std::collections::{HashMap, VecDeque};
use std::path::PathBuf;
use std::sync::Arc;
use tokio::sync::mpsc;
use super::types::{EditTarget, ProcessingSignal}; // Added EditTarget
use crate::prompt_builder::construct_task_summary_prompt; // Added
use crate::task::Task;

#[allow(clippy::too_many_arguments)] // Keep original allow if present
pub async fn run_edit_cycle(
    app_config: &AppConfig,
    llm_instance: Arc<dyn LLMClient>,
    pruned_summaries: &[ChatMessage],
    all_previous_tasks: &[Task], // History of *completed* tasks
    mut current_task_specific_messages: Vec<ChatMessage>, // Initial messages for *this* task (e.g. pruned summary, user instruction). Will be mutated.
    ordered_files: &mut OrderedFiles,
    current_display_root: &PathBuf, // New parameter
    processing_tx_opt: Option<mpsc::Sender<ProcessingSignal>>,
    retry_count: usize,
    mut forced_research_content_on_entry: Option<String>,
) -> Result<
    (
        Vec<ChatMessage>, // Final specific messages for this task
        Vec<ProposedEdit>,
        String,                                               // Overall summary
        Option<String>,                                       // Task summary
        HashMap<PathBuf, String>, // Included files content map (still returned, from last assembly)
        Option<crate::research::types::CategorizedFilePaths>, // New categorization from auto-research
    ),
    String,
> {
    if retry_count >= app_config.max_task_retries {
        let error_message = format!(
            "Task retry limit ({}) reached, stopping further retries even though auto-test failed.",
            app_config.max_task_retries
        );
        error!("{}", error_message);
        return Err(error_message);
    }
    let is_retry = retry_count > 0;

    let mut effective_auto_research_mode = app_config.auto_research_mode;
    if is_retry {
        match effective_auto_research_mode {
            crate::config::app_config::AutoResearchMode::First => {
                effective_auto_research_mode = crate::config::app_config::AutoResearchMode::False
            }
            crate::config::app_config::AutoResearchMode::FirstForcedThenFalse => {
                effective_auto_research_mode = crate::config::app_config::AutoResearchMode::False
            }
            crate::config::app_config::AutoResearchMode::FirstForcedThenTrue => {
                effective_auto_research_mode = crate::config::app_config::AutoResearchMode::True
            }
            _ => {}
        }
    }

    let user_edit_task_prompt = app_config.user_prompt.clone(); // The pure user request for this task

    // final_included_files_content_map will be updated by assemble_llm_messages
    // let mut final_included_files_content_map: HashMap<PathBuf, String> = HashMap::new(); // Will be initialized with assemble_llm_messages
    let mut new_categorization_from_research: Option<crate::research::types::CategorizedFilePaths> =
        None;
    let mut decision_model_research_loop_count: usize = 0;
    let mut expert_model_research_loop_count: usize = 0;

    

    // Initial assembly of messages for the first turn
    // `current_task_specific_messages` already contains the initial user prompt / pruned summary.
    // `assemble_llm_messages` will prepend files and historical tasks.
    let (mut llm_messages_for_this_turn, mut _final_included_files_content_map) = // Initialize both here, mark map as unused
        crate::editor::history_assembler::assemble_llm_messages(
            ordered_files,
            pruned_summaries,
            all_previous_tasks,
            &current_task_specific_messages,
            current_display_root, // Pass current_display_root
        );
    // The initial_included_map and separate assignment to final_included_files_content_map are removed.

    loop {
        // Main "turn" loop: Auto-research, Decision Model Research, Expert, Standard Planning
        let conduct_forced_research_this_iteration = forced_research_content_on_entry.is_some();

        // 1. Conduct Automatic Research
        let auto_research_summary_msg_content: String;
        let mut temp_app_config_for_research = app_config.clone();
        temp_app_config_for_research.auto_research_mode = effective_auto_research_mode;

        match crate::research::auto_research::conduct_automatic_research(
            &temp_app_config_for_research,
            llm_instance.clone(),
            all_previous_tasks,                  // Pass all_previous_tasks
            &llm_messages_for_this_turn, // Pass the fully assembled context for LLM decision
            &mut current_task_specific_messages, // Pass the evolving dialogue of the current task to append to
            ordered_files,
            &user_edit_task_prompt, // The original user request for this task
            is_retry,
            processing_tx_opt.as_ref(),
            forced_research_content_on_entry.take(), // Consumes the forced content for this attempt
        )
        .await
        {
            Ok((summary_content, categorization_opt)) => {
                auto_research_summary_msg_content = summary_content;
                if let Some(new_cat) = categorization_opt {
                    new_categorization_from_research = Some(new_cat);
                }
            }
            Err(error_msg) => {
                error!("Critical auto-research failure: {}", error_msg);
                current_task_specific_messages.push(ChatMessage {
                    // Append to current_task_specific_messages
                    role: ChatRole::System,
                    content: format!("Critical auto-research failure: {}", error_msg),
                    message_type: MessageType::Text,
                });
                if let Some(tx) = processing_tx_opt.as_ref() {
                    if tx.send(ProcessingSignal::AutoResearchEnded).await.is_err() {
                        warn!("Failed to send AutoResearchEnded signal after critical failure.");
                    }
                }
                return Err(error_msg);
            }
        }

        let research_was_not_skipped_by_config =
            !auto_research_summary_msg_content.contains("Auto-research skipped by configuration.");

        if !conduct_forced_research_this_iteration && research_was_not_skipped_by_config {
            trace!(
                "Initial auto-research phase outcome: {}",
                auto_research_summary_msg_content
            );
            // Removed push of auto_research_summary_msg_content to current_task_specific_messages
        }

        // After auto-research (which might modify ordered_files or current_task_specific_messages),
        // re-assemble the full message list for the LLM for the next step.
        (llm_messages_for_this_turn, _final_included_files_content_map) = // Direct tuple assignment
            crate::editor::history_assembler::assemble_llm_messages(
                ordered_files,
                pruned_summaries,
                all_previous_tasks,
                &current_task_specific_messages,
                current_display_root, // Pass current_display_root
            );
        // Removed intermediate updated_llm_messages and updated_included_map

        // 2. Decision Model Research Loop Check
        // This check should only run if:
        // - This isn't an iteration forced by a previous research request (avoid immediate re-check after forced research).
        // - The initial auto-research phase (step 1) actually ran and produced a result (even if no files were found, the attempt was made).
        // - The loop count for *this specific decision model* is within limits.
        if !conduct_forced_research_this_iteration && new_categorization_from_research.is_some() {
            if decision_model_research_loop_count < app_config.decision_model_auto_research_loop_max
            {
                debug!(
                    "Checking if default LLM requests further research (loop {}/{})",
                    decision_model_research_loop_count + 1,
                    app_config.decision_model_auto_research_loop_max
                );
                let instruction_for_decision_prompt = current_task_specific_messages
                    .first()
                    .map(|msg| msg.content.clone())
                    .unwrap_or_else(|| {
                        warn!("current_task_specific_messages is empty, falling back to user_edit_task_prompt for decision prompt. This might lack task_info.");
                        user_edit_task_prompt.clone() // user_edit_task_prompt is app_config.user_prompt
                    });

                let decision_prompt =
                    crate::prompt_builder::construct_pre_planning_research_decision_prompt(
                        app_config,
                        &instruction_for_decision_prompt,
                        app_config.task_info.as_deref(), // Pass task_info
                        &ordered_files
                            .get_all_labeled_files_map()
                            .values()
                            .cloned()
                            .collect::<Vec<_>>(),
                    );

                // Use the already assembled llm_messages_for_this_turn as context for the decision model
                // then append the decision_prompt.
                // The decision_prompt itself is added by temporal_chat_inference.

                // Determine LLM and context for the decision call
                let decision_making_llm_client: Arc<dyn LLMClient>;
                let context_for_decision_making_call: Vec<ChatMessage>;

                if let Some(alias) = &app_config.decision_model {
                    if alias.to_lowercase() != "none" && alias.to_lowercase() != "default" {
                        match crate::config::app_config::setup_specific_llm_client(
                            app_config, alias,
                        ) {
                            Ok(client) => {
                                log::debug!("Using specific decision model ('{}') for post-auto-research decision.", alias);
                                decision_making_llm_client = client;
                                // Context without files message for specific decision model
                                context_for_decision_making_call =
                                    crate::editor::history_assembler::assemble_llm_messages_without_files(
                                        all_previous_tasks,
                                        &current_task_specific_messages,
                                    );
                            }
                            Err(e) => {
                                log::warn!("Failed to set up decision model ('{}'): {}. Falling back to default LLM for post-auto-research decision.", alias, e);
                                decision_making_llm_client = llm_instance.clone();
                                context_for_decision_making_call =
                                    llm_messages_for_this_turn.clone(); // Full context
                            }
                        }
                    } else {
                        log::trace!("Using default LLM for post-auto-research decision (decision_model is '{}').", alias);
                        decision_making_llm_client = llm_instance.clone();
                        context_for_decision_making_call = llm_messages_for_this_turn.clone();
                        // Full context
                    }
                } else {
                    log::trace!("Using default LLM for post-auto-research decision (decision_model not set).");
                    decision_making_llm_client = llm_instance.clone();
                    // Use the same approach as specific decision model: exclude files message to avoid duplication
                    context_for_decision_making_call =
                        crate::editor::history_assembler::assemble_llm_messages_without_files(
                            all_previous_tasks,
                            &current_task_specific_messages,
                        );
                }

                match crate::llm::client::temporal_chat_inference(
                    decision_making_llm_client.as_ref(),
                    &context_for_decision_making_call,
                    &decision_prompt,
                )
                .await
                {
                    Ok(decision_response) => {
                        let research_parser =
                            crate::block_parsing::research_block::ResearchBlockParser;
                        let expectations =
                            vec![crate::block_parsing::processor::BlockExpectation {
                                parser: Box::new(research_parser),
                                expected_count:
                                    crate::block_parsing::processor::BlockCount::Optional,
                            }];

                        // The conversation_log for process_llm_response_with_blocks should be
                        // current_task_specific_messages, as it's the evolving dialogue for this task.
                        // The history_prefix is all_previous_tasks.
                        // The decision_prompt and its response need to be added to current_task_specific_messages
                        // *before* calling process_llm_response_with_blocks if they are to be part of its retry context.
                        // Or, process_llm_response_with_blocks handles adding them to its internal log for retries.
                        // Let's assume process_llm_response_with_blocks handles its own dialog for retries.
                        // We will add the successful decision dialog to current_task_specific_messages later.

                        let mut temp_log_for_decision_block_processing = Vec::new(); // For process_llm_response_with_blocks
                                                                                     // This temp_log will capture the decision prompt and response for retry purposes within block processing.
                                                                                     // It does NOT use llm_messages_for_this_turn directly.
                        temp_log_for_decision_block_processing.push(ChatMessage {
                            role: ChatRole::User,
                            content: decision_prompt.clone(),
                            message_type: MessageType::Text,
                        });
                        temp_log_for_decision_block_processing.push(ChatMessage {
                            role: ChatRole::Assistant,
                            content: decision_response.clone(),
                            message_type: crate::llm::MessageType::Text,
                        });

                        match crate::block_parsing::processor::process_llm_response_with_blocks(
                            &decision_response,
                            &expectations,
                            &decision_prompt, // Original prompt for this decision
                            &ordered_files
                                .get_all_labeled_files_map()
                                .values()
                                .cloned()
                                .collect::<Vec<_>>(),
                            decision_making_llm_client.clone(), // Use the (potentially specific) decision LLM for retries
                            &context_for_decision_making_call, // History prefix for this specific block processing call
                            &mut temp_log_for_decision_block_processing, // Log for retries within this block processing
                            app_config,
                        )
                        .await
                        {
                            Ok(processed_decision) => {
                                if let Some(research_block) = processed_decision
                                    .get_first_block_by_id(
                                        &crate::block_parsing::research_block::ResearchBlockParser
                                            .id(),
                                    )
                                {
                                    if let Ok(research_content) =
                                        crate::block_parsing::research_block::ResearchBlockParser
                                            .parse_to_string(research_block)
                                    {
                                        info!("Default LLM requested further research.");
                                        // Removed extension of temp_log_for_decision_block_processing to current_task_specific_messages
                                        // Removed push of "Default LLM requested further research." system message

                                        forced_research_content_on_entry = Some(research_content);
                                        decision_model_research_loop_count += 1;
                                        // Re-assemble llm_messages_for_this_turn before continuing
                                        (llm_messages_for_this_turn, _final_included_files_content_map) = // Direct tuple assignment
                                             crate::editor::history_assembler::assemble_llm_messages(
                                             ordered_files,
                                             pruned_summaries,
                                             all_previous_tasks,
                                             &current_task_specific_messages,
                                             current_display_root, // Pass current_display_root
                                         );
                                        continue; // Restart main loop for forced research
                                    }
                                }
                                // No research block, or parsing failed.
                                debug!("LLM decided no further research is required.");
                                // Removed extension of temp_log_for_decision_block_processing to current_task_specific_messages
                                // Removed push of "Default LLM decided no further research needed..." system message
                            }
                            Err(e) => {
                                log::warn!("Error processing decision model response for research: {}. Proceeding.", e);
                                // Removed push of "Error processing decision model response..." system message
                            }
                        }
                    }
                    Err(e) => {
                        log::warn!("Decision model LLM call failed: {}. Proceeding.", e);
                        // Removed push of "Decision model LLM call failed..." system message
                    }
                }
            } else if app_config.decision_model_auto_research_loop_max > 0 {
                // Removed push of "Maximum decision model research loops..." system message
                log::debug!(
                    "Maximum decision model research loops ({}) reached.",
                    app_config.decision_model_auto_research_loop_max
                );
            }
        }

        // After decision model, re-assemble messages if current_task_specific_messages changed
        (llm_messages_for_this_turn, _final_included_files_content_map) = // Direct tuple assignment
            crate::editor::history_assembler::assemble_llm_messages(
                ordered_files,
                pruned_summaries,
                all_previous_tasks,
                &current_task_specific_messages,
                current_display_root, // Pass current_display_root
            );

        // 3. Expert Flow
        let instruction_message_content = user_edit_task_prompt.clone(); // This is the pure user request
        let mut expert_plan_response_text_override: Option<String> = None;
        let mut direct_edits_from_expert: Vec<ProposedEdit> = Vec::new();

        let expert_can_request_research =
            expert_model_research_loop_count < app_config.expert_model_auto_research_loop_max;

        let mut effective_auto_expert_switch = app_config.auto_expert_switch;
        if is_retry {
            match effective_auto_expert_switch {
                crate::config::app_config::AutoExpertSwitch::First => {
                    effective_auto_expert_switch =
                        crate::config::app_config::AutoExpertSwitch::False
                }
                crate::config::app_config::AutoExpertSwitch::FirstForcedThenFalse => {
                    effective_auto_expert_switch =
                        crate::config::app_config::AutoExpertSwitch::False
                }
                crate::config::app_config::AutoExpertSwitch::FirstForcedThenTrue => {
                    effective_auto_expert_switch = crate::config::app_config::AutoExpertSwitch::True
                }
                _ => {}
            }
        }

        if effective_auto_expert_switch != crate::config::app_config::AutoExpertSwitch::False {
            // try_run_expert_flow needs the full assembled context for its decision model
            // and then the current_task_specific_messages to append its own dialog to.
            let mut temp_app_config_for_expert_flow = app_config.clone();
            temp_app_config_for_expert_flow.auto_expert_switch = effective_auto_expert_switch;

            match crate::expert::runner::try_run_expert_flow(
                &temp_app_config_for_expert_flow,
                llm_instance.clone(),
                None,                        // No pre-configured expert instance override
                &llm_messages_for_this_turn, // Pass full assembled context for decision
                &mut current_task_specific_messages, // Pass current task's dialogue to append to
                ordered_files,
                &instruction_message_content, // This is the pure user request
                is_retry,
                processing_tx_opt.as_ref(),
            )
            .await
            {
                Ok(ExpertFlowOutcome::PlanText {
                    plan_text,
                    research_content: _,
                }) => {
                    info!("Expert (Planning Mode) provided a plan. This plan will be used.");
                    expert_plan_response_text_override = Some(plan_text);
                    // Expert's dialog (prompt + response) is already added to current_task_specific_messages by try_run_expert_flow
                }
                Ok(ExpertFlowOutcome::ExpertEdits {
                    direct_edits,
                    research_content: _,
                }) => {
                    if !direct_edits.is_empty() {
                        info!(
                             "Expert (Editing Mode) provided {} direct 'range-replace' edits.",
                            direct_edits.len()
                        );
                        direct_edits_from_expert = direct_edits;
                    } else {
                        info!("Expert (Editing Mode) provided no usable 'range-replace' edits after processing.");
                    }
                    // Expert's dialog is added by try_run_expert_flow
                }
                Ok(ExpertFlowOutcome::OnlyResearch { research_content }) => {
                    if expert_can_request_research {
                        info!("Expert LLM requested further research.");
                        // The system message "Expert LLM requested further research " is added by try_run_expert_flow
                        forced_research_content_on_entry = Some(research_content);
                        expert_model_research_loop_count += 1;
                        // Re-assemble messages before continuing the loop for forced research
                        (llm_messages_for_this_turn, _final_included_files_content_map) = // Direct tuple assignment
                            crate::editor::history_assembler::assemble_llm_messages(
                                ordered_files,
                                pruned_summaries,
                                all_previous_tasks,
                                &current_task_specific_messages,
                                current_display_root, // Pass current_display_root
                            );
                        continue; // Restart main loop for forced research
                    } else {
                        log::info!("Expert LLM requested research, but max expert research loops reached. Ignoring research request.");
                        // This system message is also added by try_run_expert_flow
                    }
                }
                Ok(crate::expert::runner::ExpertFlowOutcome::NoAction) => {
                    log::info!("Expert flow was skipped or decided no action was needed. Proceeding with standard planning.");
                    // System message for this is added by try_run_expert_flow
                }
                Err(e) => {
                    log::error!(
                        "Error during expert flow: {}. Proceeding with standard planning.",
                        e
                    );
                    // System message for this is added by try_run_expert_flow
                }
            }
            // After expert flow, re-assemble messages if current_task_specific_messages changed
            (llm_messages_for_this_turn, _final_included_files_content_map) = // Direct tuple assignment
                crate::editor::history_assembler::assemble_llm_messages(
                    ordered_files,
                    pruned_summaries,
                    all_previous_tasks,
                    &current_task_specific_messages,
                    current_display_root, // Pass current_display_root
                );
        }

        // 4. Standard Planning / Editing (if no direct expert edits or expert plan override)
        if ordered_files.is_empty()
            && instruction_message_content.is_empty()
            && direct_edits_from_expert.is_empty()
            && expert_plan_response_text_override.is_none()
        {
            return Err("No files, no prompt, and no expert actions to act on.".to_string());
        }

        let _temp_result_handler_for_cycle = crate::result::Result::new(); // Prefixed as unused
        let mut proposed_edits_for_apply: Vec<ProposedEdit> = Vec::new();
        let mut processing_errors: Vec<String> = Vec::new();

        if !direct_edits_from_expert.is_empty() {
            proposed_edits_for_apply.extend(direct_edits_from_expert.clone());
        } else {
            // Standard planning path (if no direct expert edits)
            let mut tasks_to_process: VecDeque<EditTarget> = VecDeque::new();
            let planner_can_request_research = decision_model_research_loop_count
                < app_config.decision_model_auto_research_loop_max;

            // _handle_get_initial_plan will use _llm_messages_for_this_turn for its LLM call context
            // and append its dialog to current_task_specific_messages.
            let initial_plan_result = super::initial_plan::_handle_get_initial_plan(
                app_config,
                llm_instance.clone(),
                &llm_messages_for_this_turn, // Pass the full assembled context
                &mut current_task_specific_messages, // Pass current task's dialogue to append to
                &mut crate::result::Result::new(), // Pass a temporary AppResult, as it's unused
                ordered_files,
                &instruction_message_content, // This is the pure user request
                &mut tasks_to_process,
                processing_tx_opt.as_ref(),
                expert_plan_response_text_override,
                planner_can_request_research,
            )
            .await;

            // After _handle_get_initial_plan, re-assemble messages if current_task_specific_messages changed
            (llm_messages_for_this_turn, _final_included_files_content_map) = // Direct tuple assignment
                crate::editor::history_assembler::assemble_llm_messages(
                    ordered_files,
                    pruned_summaries,
                    all_previous_tasks,
                    &current_task_specific_messages,
                    current_display_root, // Pass current_display_root
                );

            match initial_plan_result {
                Ok(Some(research_content_from_plan)) => {
                    log::info!("Standard planner LLM requested further research.");
                    // System message "Standard planner LLM requested further research." is added by _handle_get_initial_plan
                    forced_research_content_on_entry = Some(research_content_from_plan);
                    decision_model_research_loop_count += 1;
                    // Re-assemble messages before continuing the loop for forced research
                    (llm_messages_for_this_turn, _final_included_files_content_map) = // Direct tuple assignment
                        crate::editor::history_assembler::assemble_llm_messages(
                            ordered_files,
                            pruned_summaries,
                            all_previous_tasks,
                            &current_task_specific_messages,
                            current_display_root, // Pass current_display_root
                        );
                    continue;
                }
                Ok(None) => { /* No research requested. */ }
                Err(e_msg) => {
                    return Err(format!("Failed to get initial editing plan: {}", e_msg));
                }
            }
            let mut completed_targets_count = 0;
            let total_targets_from_queue = tasks_to_process.len();
            let mut current_target_idx_for_signal = 0;

            while let Some(target_info) = tasks_to_process.pop_front() {
                if let Some(tx) = processing_tx_opt.as_ref() {
                    if tx
                        .send(ProcessingSignal::TargetProcessingStart {
                            target_index: current_target_idx_for_signal,
                        })
                        .await
                        .is_err()
                    {
                        warn!(
                            "Failed to send TargetProcessingStart signal for target index {}",
                            current_target_idx_for_signal
                        );
                    }
                }
                // _handle_process_target will use _llm_messages_for_this_turn for its context
                // and append its dialog to current_task_specific_messages.
                let process_result = super::target_processor::_handle_process_target(
                    app_config,
                    llm_instance.clone(),
                    &llm_messages_for_this_turn, // Pass full assembled context
                    &mut current_task_specific_messages, // Pass current task's dialogue to append to
                    ordered_files, // Pass OrderedFiles by reference (already mutable)
                    &target_info,
                )
                .await;

                // After each target processing, re-assemble messages if current_task_specific_messages changed
                (llm_messages_for_this_turn, _final_included_files_content_map) = // Direct tuple assignment
                    crate::editor::history_assembler::assemble_llm_messages(
                        ordered_files,
                        pruned_summaries,
                        all_previous_tasks,
                        &current_task_specific_messages,
                        current_display_root, // Pass current_display_root
                    );

                let target_processed_successfully = match &process_result {
                    Ok((_, delimiter_ok)) => *delimiter_ok,
                    Err(_) => false,
                };

                match process_result {
                    Ok((Some(proposed_edit), _)) => proposed_edits_for_apply.push(proposed_edit),
                    Ok((None, _)) => {}
                    Err(e_msg) => {
                        let file_name_display = target_info
                            .file_path
                            .file_name()
                            .map(|name| name.to_string_lossy())
                            .unwrap_or_else(|| std::borrow::Cow::from("<unknown_file_name>"));
                        log::error!(
                            "Error processing target {}: {}",
                            target_info.file_path.display(),
                            file_name_display
                        );
                        processing_errors.push(format!(
                            "Error for target {} ({}): {}",
                            target_info.file_path.display(),
                            file_name_display,
                            e_msg
                        ));
                    }
                }
                if let Some(tx) = processing_tx_opt.as_ref() {
                    if tx
                        .send(ProcessingSignal::TargetProcessed {
                            target_index: current_target_idx_for_signal,
                            success: target_processed_successfully,
                        })
                        .await
                        .is_err()
                    {
                        warn!(
                            "Failed to send TargetProcessed signal for target index {}",
                            current_target_idx_for_signal
                        );
                    }
                }
                completed_targets_count += 1;
                current_target_idx_for_signal += 1;
                let total_for_calc = if total_targets_from_queue > 0 {
                    total_targets_from_queue
                } else {
                    1
                };
                let base_progress = 0.20; // Assuming auto-research and decision took up to 20%
                let edit_phase_progress_span = 0.75; // Editing takes up to 75%
                let current_target_phase_progress = (completed_targets_count as f32
                    / total_for_calc as f32)
                    * edit_phase_progress_span;
                let overall_progress = base_progress + current_target_phase_progress;
                if let Some(tx) = processing_tx_opt.as_ref() {
                    if tx
                        .send(ProcessingSignal::UpdateProgress(overall_progress.min(0.95)))
                        .await
                        .is_err()
                    {
                        warn!(
                            "Failed to send progress update after processing target {}",
                            completed_targets_count
                        );
                    }
                }
            }
        }

        // --- Advanced Syntax Check and Final Edit Aggregation ---
        // The standalone delimiter check has been removed.
        // Syntax checking (if enabled) will now internally decide which retry prompt to use
        // based on the nature of the `syn` error.

        let mut final_validated_edits: Vec<ProposedEdit> = Vec::new();
        let mut edits_by_file: HashMap<PathBuf, Vec<ProposedEdit>> = HashMap::new();
        for edit in proposed_edits_for_apply {
            // Iterate directly over proposed_edits_for_apply
            edits_by_file
                .entry(edit.target.file_path.clone())
                .or_default()
                .push(edit);
        }

        for (file_path, edits_for_file) in edits_by_file {
            let is_rust_file = file_path.extension().map_or(false, |ext| ext == "rs");
            let rust_syntax_check_enabled = app_config // Renamed variable for clarity
                .advanced_language_features
                .contains("rust-advanced-syntax-checking ");

            if is_rust_file && rust_syntax_check_enabled {
                // Use renamed variable
                let validation_result =
                    crate::language_integrations::rust::syntax_checker::validate_and_retry_rust_edits(
                        &file_path,
                        edits_for_file, // Pass ownership
                        ordered_files,
                        app_config,
                        llm_instance.clone(),
                        &llm_messages_for_this_turn,
                    )
                    .await;

                match validation_result {
                    crate::language_integrations::rust::syntax_checker::SyntaxCheckResult::Success { new_edits, .. } => {
                        debug!("Advanced syntax check passed for {}", file_path.display());
                        final_validated_edits.extend(new_edits);
                    }
                    crate::language_integrations::rust::syntax_checker::SyntaxCheckResult::Failure { error_message } => {
                        warn!("Final syntax check failed for {}: {}. Edits for this file will be discarded.", file_path.display(), error_message);
                        processing_errors.push(format!("Syntax check failed for {}: {}", file_path.display(), error_message));
                        // System message about the failure is added to conversation_log inside the retry logic.
                    }
                    crate::language_integrations::rust::syntax_checker::SyntaxCheckResult::RetryFailedUseOriginal { original_edits, failure_reason } => {
                        warn!(
                            "Advanced syntax check for {} indicated LLM retry failed (reason: {}). Using original edits for this file, which are likely problematic.", 
                            file_path.display(),
                            failure_reason
                        );
                        // These original_edits are the ones that initially failed the syntax check,
                        // and the LLM failed to provide a better alternative.
                        // The auto-test loop will catch this if they still cause issues.
                        final_validated_edits.extend(original_edits);
                        // Optionally, add to processing_errors if this should be surfaced as an error.
                        // For now, we proceed with the original edits as per the logic in syntax_checker.
                        // processing_errors.push(format!("Syntax check LLM retry failed for {}: {}", file_path.display(), failure_reason));
                    }
                }
            } else {
                // Not a Rust file or syntax check feature is disabled, so pass edits through.
                final_validated_edits.extend(edits_for_file);
            }
        }
        // --- End of Syntax Check ---

        let overall_summary_message = if !processing_errors.is_empty() {
            format!("Completed with errors: {}", processing_errors.join("; "))
        } else if final_validated_edits.is_empty() {
            // Check the last assistant message in current_task_specific_messages
            if current_task_specific_messages
                .iter()
                .rev()
                .find(|m| m.role == crate::llm::ChatRole::Assistant)
                .map_or(false, |m| {
                    m.content
                        .contains("did not suggest any actionable edit targets ")
                })
            {
                "LLM proposed no edits.".to_string()
            } else {
                "Processing complete, no valid edits proposed or passed validation.".to_string()
            }
        } else {
            format!("Proposed {} validated edits.", final_validated_edits.len())
        };

        let mut generated_task_summary: Option<String> = None;
        if processing_errors.is_empty() {
            let task_summary_request_prompt = construct_task_summary_prompt(
                app_config,
                &final_validated_edits,
                &user_edit_task_prompt, // Use the original user prompt for this task
            );
            log::trace!(
                "Requesting LLM for task summary with prompt:\n{}",
                task_summary_request_prompt
            );

            // Determine LLM and context for summary
            let (summary_llm_client, context_for_task_summary_request) = if let Some(
                summary_alias,
            ) =
                &app_config.summary_model
            {
                if summary_alias.to_lowercase() != "none"
                    && summary_alias.to_lowercase() != "default"
                {
                    match crate::config::app_config::setup_specific_llm_client(
                        app_config,
                        summary_alias,
                    ) {
                        Ok(client) => {
                            log::trace!("Using specific summary model (\"{}\") for task summary with minimal context.", summary_alias);
                            (client, vec![]) // Minimal context for dedicated summary model
                        }
                        Err(e) => {
                            log::warn!("Failed to set up summary model \"{}\": {}. Falling back to default LLM and full assembled context for task summary.", summary_alias, e);
                            (llm_instance.clone(), llm_messages_for_this_turn.clone())
                            // Fallback to default LLM and full context of this turn
                        }
                    }
                } else {
                    log::trace!("Using default LLM for task summary with full assembled context (summary_model is 'none' or 'default').");
                    (llm_instance.clone(), llm_messages_for_this_turn.clone())
                }
            } else {
                log::trace!("Using default LLM for task summary with full assembled context (summary_model not set).");
                (llm_instance.clone(), llm_messages_for_this_turn.clone())
            };

            // The conversation_log for block processing should reflect what was actually sent to the LLM.
            // If using a dedicated summary model, this context is minimal.
            let mut temp_log_for_summary_block_processing =
                context_for_task_summary_request.clone();
            // The prompt is added by temporal_chat_inference. The response will be added before block processing.

            match crate::llm::client::temporal_chat_inference(
                summary_llm_client.as_ref(),
                &context_for_task_summary_request, // Context for the LLM call
                &task_summary_request_prompt,
            )
            .await
            {
                Ok(summary_response) => {
                    log::trace!("Raw LLM response for task summary:\n{}", summary_response);
                    let summary_expectations =
                        vec![crate::block_parsing::processor::BlockExpectation {
                            parser: Box::new(
                                crate::block_parsing::summary_blocks::SummaryBlockParser,
                            ),
                            expected_count: crate::block_parsing::processor::BlockCount::Exact(1),
                        }];
                    let mut temp_conversation_log_for_summary_parsing =
                        context_for_task_summary_request.clone(); // Corrected variable name
                    temp_conversation_log_for_summary_parsing.push(ChatMessage {
                        role: ChatRole::User,
                        content: task_summary_request_prompt.clone(),
                        message_type: crate::llm::MessageType::Text,
                    });
                    temp_conversation_log_for_summary_parsing.push(ChatMessage {
                        role: ChatRole::Assistant,
                        content: summary_response.clone(),
                        message_type: crate::llm::MessageType::Text,
                    });
                    match crate::block_parsing::processor::process_llm_response_with_blocks(
                        &summary_response,
                        &summary_expectations,
                        &task_summary_request_prompt,
                        &[],
                        llm_instance.clone(), // Clone Arc
                        &[],
                        &mut temp_conversation_log_for_summary_parsing,
                        app_config,
                    )
                    .await
                    {
                        Ok(summary_processed_output) => {
                            if let Some(summary_raw_blocks) = summary_processed_output
                                .successfully_parsed_blocks
                                .get(&crate::block_parsing::summary_blocks::SummaryBlockParser.id())
                            {
                                if let Some(raw_block) = summary_raw_blocks.first() {
                                    match crate::block_parsing::summary_blocks::SummaryBlockParser.parse_to_string(raw_block) {
                                        Ok(summary_text) if !summary_text.is_empty() => generated_task_summary = Some(summary_text),
                                        Ok(_) => log::trace!("LLM provided an empty summary block for the task."),
                                        Err(e) => log::trace!("Failed to parse content of validated task summary block: {}", e),
                                    }
                                } else {
                                    log::trace!("LLM response for task summary did not contain the expected summary block after processing.");
                                }
                            } else {
                                log::trace!("No summary block found in LLM's response for task summary request after processing.");
                            }
                            if !summary_processed_output.remaining_raw_blocks.is_empty() {
                                log::trace!(
                                    "Unclaimed raw blocks found in LLM task summary response: {:?}",
                                    summary_processed_output.remaining_raw_blocks
                                );
                            }
                        }
                        Err(e) => {
                            log::trace!("Error processing LLM response for task summary: {}", e)
                        }
                    }
                    // Add the summary request and response to current_task_specific_messages
                    current_task_specific_messages.extend(
                        temp_log_for_summary_block_processing
                            .drain(context_for_task_summary_request.len()..),
                    );
                }
                Err(e) => log::trace!("Failed to get task summary from LLM: {}", e),
            }
        }
        if let Some(summary_text) = &generated_task_summary {
            log::trace!("Edits Summary Received: {}", summary_text);
        } else {
            log::trace!("No task summary was generated or parsed.");
        }

        // --- Temporary Debugging: Save full prompt context to task.tmp ---
        let (debug_messages_to_save, _) = crate::editor::history_assembler::assemble_llm_messages(
            ordered_files, // Final state of ordered_files
            pruned_summaries,
            all_previous_tasks,
            &current_task_specific_messages, // This is the final dialogue for the completed task
            current_display_root,
        );
        match serde_json::to_string_pretty(&debug_messages_to_save) {
            Ok(json_data) => {
                // Attempt to write asynchronously. If in a context where blocking is fine,
                // std::fs::write could be used, but tokio::fs is generally safer in async fn.
                match tokio::fs::write("task.tmp", json_data).await {
                    Ok(_) => info!("Debug: Saved full prompt context to task.tmp"),
                    Err(e) => warn!("Debug: Failed to write task.tmp: {}", e),
                }
            }
            Err(e) => {
                warn!("Debug: Failed to serialize messages for task.tmp: {}", e);
            }
        }
        // --- End Temporary Debugging ---

        return Ok((
            current_task_specific_messages, // Return the final dialogue for this task
            final_validated_edits,
            overall_summary_message,
            generated_task_summary,
            _final_included_files_content_map, // From the last assembly
            new_categorization_from_research,
        ));
    } // End of main loop
}
